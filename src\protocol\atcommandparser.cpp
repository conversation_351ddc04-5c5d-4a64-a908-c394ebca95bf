#include "atcommandparser.h"
#include <QDebug>

AtCommandParser::AtCommandParser(QObject *parent)
    : QObject(parent)
    , m_lineEnding("\r\n")
    , m_responseTimeout(5000)
{
    m_bufferTimer = new QTimer(this);
    m_bufferTimer->setSingleShot(true);
    connect(m_bufferTimer, &QTimer::timeout, this, &AtCommandParser::onBufferTimeout);

    initRegexPatterns();
}

AtCommandParser::~AtCommandParser()
{
}

AtCommand AtCommandParser::parseCommand(const QString& command)
{
    AtCommand atCmd;
    if (atCmd.parse(command)) {
        return atCmd;
    }

    return AtCommand(); // 返回无效指令
}

AtResponse AtCommandParser::parseResponse(const QString& response)
{
    AtResponse atResp;
    if (atResp.parse(response)) {
        return atResp;
    }

    return AtResponse(); // 返回无效响应
}

QString AtCommandParser::formatCommand(const AtCommand& command)
{
    if (!command.isValid()) {
        return QString();
    }

    return command.format() + m_lineEnding;
}

QString AtCommandParser::formatResponse(const AtResponse& response)
{
    if (!response.isValid()) {
        return QString();
    }

    return m_lineEnding + response.format() + m_lineEnding;
}

bool AtCommandParser::validateCommand(const AtCommand& command)
{
    if (!command.isValid()) {
        return false;
    }

    // 根据不同的指令类型进行验证
    switch (command.getCommand()) {
    case MqttAtCommand::AT:
        // AT指令不需要参数
        return command.getParameterCount() == 0;

    case MqttAtCommand::QMTOPEN:
        // AT+QMTOPEN=<client_idx>,"<host_name>",<port>
        if (command.getType() == AtCommandType::Set) {
            return command.getParameterCount() == 3;
        } else if (command.getType() == AtCommandType::Query) {
            return command.getParameterCount() == 0;
        }
        break;

    case MqttAtCommand::QMTCLOSE:
        // AT+QMTCLOSE=<client_idx>
        if (command.getType() == AtCommandType::Set) {
            return command.getParameterCount() == 1;
        }
        break;

    case MqttAtCommand::QMTCONN:
        // AT+QMTCONN=<client_idx>,"<clientid>"[,<username>,<password>]
        if (command.getType() == AtCommandType::Set) {
            int paramCount = command.getParameterCount();
            return paramCount >= 2 && paramCount <= 4;
        } else if (command.getType() == AtCommandType::Query) {
            return command.getParameterCount() == 0;
        }
        break;

    case MqttAtCommand::QMTDISC:
        // AT+QMTDISC=<client_idx>
        if (command.getType() == AtCommandType::Set) {
            return command.getParameterCount() == 1;
        }
        break;

    case MqttAtCommand::QMTSUB:
        // AT+QMTSUB=<client_idx>,<msgid>,"<topic>",<qos>
        if (command.getType() == AtCommandType::Set) {
            return command.getParameterCount() == 4;
        } else if (command.getType() == AtCommandType::Test) {
            return command.getParameterCount() == 0;
        }
        break;

    case MqttAtCommand::QMTUNSUB:
        // AT+QMTUNSUB=<client_idx>,<msgid>,"<topic>"
        if (command.getType() == AtCommandType::Set) {
            return command.getParameterCount() == 3;
        }
        break;

    case MqttAtCommand::QMTPUB:
        // AT+QMTPUB=<client_idx>,<msgid>,<qos>,<retain>,"<topic>","<msg>"
        if (command.getType() == AtCommandType::Set) {
            return command.getParameterCount() == 6;
        }
        break;

    case MqttAtCommand::QMTPUBEX:
        // AT+QMTPUBEX=<client_idx>,<msgid>,<qos>,<retain>,"<topic>",<msg_length>
        if (command.getType() == AtCommandType::Set) {
            return command.getParameterCount() == 6;
        }
        break;

    case MqttAtCommand::QMTCFG:
        // AT+QMTCFG="<cfg_type>",<client_idx>[,<cfg_value>]
        if (command.getType() == AtCommandType::Set) {
            return command.getParameterCount() >= 2;
        }
        break;

    default:
        return false;
    }

    return true;
}

void AtCommandParser::processReceivedData(const QByteArray& data)
{
    m_buffer.append(data);

    // 重启缓冲区超时定时器
    m_bufferTimer->start(m_responseTimeout);

    // 处理缓冲区中的完整行
    while (true) {
        int lineEndPos = m_buffer.indexOf(m_lineEnding.toUtf8());
        if (lineEndPos == -1) {
            // 没有完整的行，等待更多数据
            break;
        }

        // 提取一行数据
        QByteArray lineData = m_buffer.left(lineEndPos);
        m_buffer.remove(0, lineEndPos + m_lineEnding.length());

        // 处理这一行
        QString line = QString::fromUtf8(lineData);
        processLine(line);
    }
}

void AtCommandParser::setLineEnding(const QString& lineEnding)
{
    m_lineEnding = lineEnding;
}

QString AtCommandParser::getLineEnding() const
{
    return m_lineEnding;
}

void AtCommandParser::clearBuffer()
{
    m_buffer.clear();
    m_bufferTimer->stop();
}

void AtCommandParser::setResponseTimeout(int timeout)
{
    m_responseTimeout = timeout;
}

int AtCommandParser::getResponseTimeout() const
{
    return m_responseTimeout;
}

void AtCommandParser::onBufferTimeout()
{
    if (!m_buffer.isEmpty()) {
        // 处理缓冲区中的数据
        logWarnning("Buffer timeout, clear buffer data: ") << m_buffer;
        m_buffer.clear();
    }
}

void AtCommandParser::processLine(const QString& line)
{
    QString cleanLine = cleanString(line);
    if (cleanLine.isEmpty()) {
        return;
    }

    if (isAtCommand(cleanLine)) {
        // 解析AT指令
        AtCommand command = parseCommand(cleanLine);
        if (command.isValid()) {
            emit commandParsed(command);
        }
    } else if (isAtResponse(cleanLine)) {
        // 解析AT响应
        AtResponse response = parseResponse(cleanLine);
        if (response.isValid()) {
            if (response.isUrc()) {
                emit urcReceived(response);
            } else {
                emit responseParsed(response);
            }
        }
    } else if (isUrc(cleanLine)) {
        // 解析URC
        AtResponse response = parseResponse(cleanLine);
        if (response.isValid()) {
            emit urcReceived(response);
        }
    } else {
        // 其他数据，可能是数据响应
        AtResponse response(AtResponseType::Data, cleanLine);
        emit responseParsed(response);
    }
}

bool AtCommandParser::isAtCommand(const QString& line) const
{
    return m_atCommandRegex.match(line).hasMatch();
}

bool AtCommandParser::isAtResponse(const QString& line) const
{
    return m_atResponseRegex.match(line).hasMatch();
}

bool AtCommandParser::isUrc(const QString& line) const
{
    return m_urcRegex.match(line).hasMatch();
}

QString AtCommandParser::cleanString(const QString& str) const
{
    QString cleaned = str;

    // 移除控制字符
    cleaned = cleaned.remove(QRegularExpression("[\\x00-\\x1F\\x7F]"));

    // 移除首尾空白
    cleaned = cleaned.trimmed();

    return cleaned;
}

void AtCommandParser::initRegexPatterns()
{
    // AT指令正则表达式: AT 或 AT+COMMAND...
    m_atCommandRegex.setPattern("^AT(\\+\\w+.*)?$");
    m_atCommandRegex.setPatternOptions(QRegularExpression::CaseInsensitiveOption);

    // AT响应正则表达式: OK, ERROR, >
    m_atResponseRegex.setPattern("^(OK|ERROR|>)$");
    m_atResponseRegex.setPatternOptions(QRegularExpression::CaseInsensitiveOption);

    // URC正则表达式: +COMMAND: ...
    m_urcRegex.setPattern("^\\+\\w+:");
    m_urcRegex.setPatternOptions(QRegularExpression::CaseInsensitiveOption);
}
