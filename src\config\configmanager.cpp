#include "configmanager.h"
#include <QFile>
#include <QDir>
#include <QJsonParseError>
#include "log.h"
#include <QMutexLocker>

// 静态成员初始化
ConfigManager* ConfigManager::m_instance = nullptr;
QMutex ConfigManager::m_mutex;

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent)
{
    // 初始化默认配置
    resetToDefault();
}

ConfigManager::~ConfigManager()
{
}

ConfigManager* ConfigManager::instance()
{
    if (m_instance == nullptr) {
        QMutexLocker locker(&m_mutex);
        if (m_instance == nullptr) {
            m_instance = new ConfigManager();
        }
    }
    return m_instance;
}

void ConfigManager::destroyInstance()
{
    QMutexLocker locker(&m_mutex);
    if (m_instance != nullptr) {
        delete m_instance;
        m_instance = nullptr;
    }
}

bool ConfigManager::loadConfig(const QString& filePath)
{
    QMutexLocker locker(&m_configMutex);

    m_configFilePath = filePath;

    QFile file(filePath);
    if (!file.exists()) {
        logWarnning("Configuration file does not exist: " << filePath << ", using default configuration");
        resetToDefault();
        // 创建默认配置文件 - 使用内部函数避免死锁
        saveConfigInternal(filePath);
        emit configLoaded(true);
        return true;
    }

    if (!file.open(QIODevice::ReadOnly)) {
        logError("Unable to open configuration file: " << filePath << ", error: " << file.errorString());
        emit configLoaded(false);
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        logError("Configuration file JSON parsing error: " << parseError.errorString());
        emit configLoaded(false);
        return false;
    }

    if (!doc.isObject()) {
        logError("Configuration file format error: root node is not a JSON object");
        emit configLoaded(false);
        return false;
    }

    try {
        loadFromJson(doc.object());

        if (!validateConfig()) {
            logWarnning("Configuration validation failed, using default configuration");
            resetToDefault();
        }

        emit configLoaded(true);
        emit configChanged();
        return true;
    } catch (const std::exception& e) {
        logError("Exception occurred while loading configuration: " << e.what());
        resetToDefault();
        emit configLoaded(false);
        return false;
    }
}

bool ConfigManager::saveConfig(const QString& filePath)
{
    QMutexLocker locker(&m_configMutex);
    return saveConfigInternal(filePath);
}

bool ConfigManager::saveConfigInternal(const QString& filePath)
{
    QString saveFilePath = filePath.isEmpty() ? m_configFilePath : filePath;

    // 确保目录存在
    QFileInfo fileInfo(saveFilePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            logError("Unable to create configuration file directory: " << dir.absolutePath());
            return false;
        }
    }

    QFile file(saveFilePath);
    if (!file.open(QIODevice::WriteOnly)) {
        logError("Unable to create configuration file: " << saveFilePath << ", error: " << file.errorString());
        return false;
    }

    QJsonDocument doc(saveToJson());
    QByteArray data = doc.toJson(QJsonDocument::Indented);

    qint64 bytesWritten = file.write(data);
    file.close();

    if (bytesWritten != data.size()) {
        logError("Configuration file write incomplete");
        return false;
    }

    logInfo("Configuration file saved successfully: " << saveFilePath);
    return true;
}

AppConfig ConfigManager::getConfig() const
{
    QMutexLocker locker(&m_configMutex);
    return m_config;
}

void ConfigManager::setConfig(const AppConfig& config)
{
    QMutexLocker locker(&m_configMutex);
    m_config = config;
    emit configChanged();
}

SerialConfig ConfigManager::getSerialConfig() const
{
    QMutexLocker locker(&m_configMutex);
    return m_config.serial;
}

void ConfigManager::setSerialConfig(const SerialConfig& config)
{
    QMutexLocker locker(&m_configMutex);
    m_config.serial = config;
    emit configChanged();
}

LogConfig ConfigManager::getLogConfig() const
{
    QMutexLocker locker(&m_configMutex);
    return m_config.log;
}

void ConfigManager::setLogConfig(const LogConfig& config)
{
    QMutexLocker locker(&m_configMutex);
    m_config.log = config;
    emit configChanged();
}

MqttConfig ConfigManager::getMqttConfig() const
{
    QMutexLocker locker(&m_configMutex);
    return m_config.mqtt;
}

void ConfigManager::setMqttConfig(const MqttConfig& config)
{
    QMutexLocker locker(&m_configMutex);
    m_config.mqtt = config;
    emit configChanged();
}

void ConfigManager::resetToDefault()
{
    m_config = AppConfig();
}

bool ConfigManager::validateConfig() const
{
    // 验证串口配置
    if (m_config.serial.portName.isEmpty()) {
        logWarnning("Serial port name cannot be empty");
        return false;
    }

    if (m_config.serial.readTimeout <= 0 || m_config.serial.writeTimeout <= 0) {
        logWarnning("Serial port timeout must be greater than 0");
        return false;
    }

    // 验证日志配置
    if (m_config.log.maxFileSize <= 0 || m_config.log.maxFileCount <= 0) {
        logWarnning("Log file size and count must be greater than 0");
        return false;
    }

    // 验证MQTT配置
    if (m_config.mqtt.port <= 0 || m_config.mqtt.port > 65535) {
        logWarnning("Invalid MQTT port number");
        return false;
    }

    if (m_config.mqtt.keepAlive <= 0) {
        logWarnning("MQTT keep alive interval must be greater than 0");
        return false;
    }

    return true;
}

void ConfigManager::loadFromJson(const QJsonObject& jsonObj)
{
    if (jsonObj.contains("serial") && jsonObj["serial"].isObject()) {
        loadSerialConfig(jsonObj["serial"].toObject());
    }

    if (jsonObj.contains("log") && jsonObj["log"].isObject()) {
        loadLogConfig(jsonObj["log"].toObject());
    }

    if (jsonObj.contains("mqtt") && jsonObj["mqtt"].isObject()) {
        loadMqttConfig(jsonObj["mqtt"].toObject());
    }
}

QJsonObject ConfigManager::saveToJson() const
{
    QJsonObject jsonObj;
    jsonObj["serial"] = saveSerialConfig();
    jsonObj["log"] = saveLogConfig();
    jsonObj["mqtt"] = saveMqttConfig();
    return jsonObj;
}

void ConfigManager::loadSerialConfig(const QJsonObject& jsonObj)
{
    if (jsonObj.contains("portName")) {
        m_config.serial.portName = jsonObj["portName"].toString();
    }

    if (jsonObj.contains("baudRate")) {
        int baudRate = jsonObj["baudRate"].toInt();
        m_config.serial.baudRate = static_cast<QSerialPort::BaudRate>(baudRate);
    }

    if (jsonObj.contains("dataBits")) {
        int dataBits = jsonObj["dataBits"].toInt();
        m_config.serial.dataBits = static_cast<QSerialPort::DataBits>(dataBits);
    }

    if (jsonObj.contains("parity")) {
        int parity = jsonObj["parity"].toInt();
        m_config.serial.parity = static_cast<QSerialPort::Parity>(parity);
    }

    if (jsonObj.contains("stopBits")) {
        int stopBits = jsonObj["stopBits"].toInt();
        m_config.serial.stopBits = static_cast<QSerialPort::StopBits>(stopBits);
    }

    if (jsonObj.contains("flowControl")) {
        int flowControl = jsonObj["flowControl"].toInt();
        m_config.serial.flowControl = static_cast<QSerialPort::FlowControl>(flowControl);
    }

    if (jsonObj.contains("readTimeout")) {
        m_config.serial.readTimeout = jsonObj["readTimeout"].toInt();
    }

    if (jsonObj.contains("writeTimeout")) {
        m_config.serial.writeTimeout = jsonObj["writeTimeout"].toInt();
    }
}

void ConfigManager::loadLogConfig(const QJsonObject& jsonObj)
{
    if (jsonObj.contains("logLevel")) {
        m_config.log.logLevel = jsonObj["logLevel"].toString();
    }

    if (jsonObj.contains("logFilePath")) {
        m_config.log.logFilePath = jsonObj["logFilePath"].toString();
    }

    if (jsonObj.contains("maxFileSize")) {
        m_config.log.maxFileSize = jsonObj["maxFileSize"].toInt();
    }

    if (jsonObj.contains("maxFileCount")) {
        m_config.log.maxFileCount = jsonObj["maxFileCount"].toInt();
    }

    if (jsonObj.contains("enableConsoleOutput")) {
        m_config.log.enableConsoleOutput = jsonObj["enableConsoleOutput"].toBool();
    }
}

void ConfigManager::loadMqttConfig(const QJsonObject& jsonObj)
{
    if (jsonObj.contains("host")) {
        m_config.mqtt.host = jsonObj["host"].toString();
    }

    if (jsonObj.contains("port")) {
        m_config.mqtt.port = static_cast<quint16>(jsonObj["port"].toInt());
    }

    if (jsonObj.contains("clientId")) {
        m_config.mqtt.clientId = jsonObj["clientId"].toString();
    }

    if (jsonObj.contains("username")) {
        m_config.mqtt.username = jsonObj["username"].toString();
    }

    if (jsonObj.contains("password")) {
        m_config.mqtt.password = jsonObj["password"].toString();
    }

    if (jsonObj.contains("keepAlive")) {
        m_config.mqtt.keepAlive = jsonObj["keepAlive"].toInt();
    }

    if (jsonObj.contains("cleanSession")) {
        m_config.mqtt.cleanSession = jsonObj["cleanSession"].toBool();
    }
}

QJsonObject ConfigManager::saveSerialConfig() const
{
    QJsonObject jsonObj;
    jsonObj["portName"] = m_config.serial.portName;
    jsonObj["baudRate"] = static_cast<int>(m_config.serial.baudRate);
    jsonObj["dataBits"] = static_cast<int>(m_config.serial.dataBits);
    jsonObj["parity"] = static_cast<int>(m_config.serial.parity);
    jsonObj["stopBits"] = static_cast<int>(m_config.serial.stopBits);
    jsonObj["flowControl"] = static_cast<int>(m_config.serial.flowControl);
    jsonObj["readTimeout"] = m_config.serial.readTimeout;
    jsonObj["writeTimeout"] = m_config.serial.writeTimeout;
    return jsonObj;
}

QJsonObject ConfigManager::saveLogConfig() const
{
    QJsonObject jsonObj;
    jsonObj["logLevel"] = m_config.log.logLevel;
    jsonObj["logFilePath"] = m_config.log.logFilePath;
    jsonObj["maxFileSize"] = m_config.log.maxFileSize;
    jsonObj["maxFileCount"] = m_config.log.maxFileCount;
    jsonObj["enableConsoleOutput"] = m_config.log.enableConsoleOutput;
    return jsonObj;
}

QJsonObject ConfigManager::saveMqttConfig() const
{
    QJsonObject jsonObj;
    jsonObj["host"] = m_config.mqtt.host;
    jsonObj["port"] = static_cast<int>(m_config.mqtt.port);
    jsonObj["clientId"] = m_config.mqtt.clientId;
    jsonObj["username"] = m_config.mqtt.username;
    jsonObj["password"] = m_config.mqtt.password;
    jsonObj["keepAlive"] = m_config.mqtt.keepAlive;
    jsonObj["cleanSession"] = m_config.mqtt.cleanSession;
    return jsonObj;
}
