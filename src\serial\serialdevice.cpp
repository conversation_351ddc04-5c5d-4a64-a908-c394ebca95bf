#include "serialdevice.h"
#include <QSerialPortInfo>
#include "log.h"

SerialDevice::SerialDevice(QObject *parent)
    : QObject(parent)
    , m_serialPort(nullptr)
    , m_writeTimer(nullptr)
    , m_readTimeout(3000)
    , m_writeTimeout(3000)
{
    m_serialPort = new QSerialPort(this);
    m_writeTimer = new QTimer(this);
    m_writeTimer->setSingleShot(true);

    // 连接信号
    connect(m_serialPort, &QSerialPort::readyRead, this, &SerialDevice::onDataReceived);
    connect(m_serialPort, QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::errorOccurred),
            this, &SerialDevice::onSerialError);
    connect(m_writeTimer, &QTimer::timeout, this, &SerialDevice::onWriteTimeout);
}

SerialDevice::~SerialDevice()
{
    SerialDevice::close();  // 明确调用当前类的实现
}

bool SerialDevice::open()
{
    if (m_serialPort->isOpen()) {
        return true;
    }

    if (m_serialPort->portName().isEmpty()) {
        setLastError("Serial port name not set");
        return false;
    }

    if (!m_serialPort->open(QIODevice::ReadWrite)) {
        setLastError(QString("Unable to open serial port %1: %2").arg(m_serialPort->portName(), m_serialPort->errorString()));
        return false;
    }

    // 清空缓冲区
    clear();

    logInfo("Serial port opened: " << m_serialPort->portName()
            << " Baud rate: " << m_serialPort->baudRate()
            << " Data bits: " << m_serialPort->dataBits()
            << " Parity: " << m_serialPort->parity()
            << " Stop bits: " << m_serialPort->stopBits()
            << " Flow control: " << m_serialPort->flowControl());

    emit connectionChanged(true);
    return true;
}

void SerialDevice::close()
{
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->close();
        logInfo("Serial port closed: " << m_serialPort->portName());
        emit connectionChanged(false);
    }

    if (m_writeTimer && m_writeTimer->isActive()) {
        m_writeTimer->stop();
    }
}

bool SerialDevice::isOpen() const
{
    return m_serialPort && m_serialPort->isOpen();
}

qint64 SerialDevice::write(const QByteArray& data)
{
    if (!isOpen()) {
        setLastError("Serial port not open");
        return -1;
    }

    if (data.isEmpty()) {
        logWarnning("Serial port write empty data");
        return 0;
    }

    qint64 bytesWritten = m_serialPort->write(data);

    if (bytesWritten == -1) {
        setLastError(QString("Failed to write data: %1").arg(m_serialPort->errorString()));
        return -1;
    }

    // 等待数据写入完成
    if (!m_serialPort->waitForBytesWritten(m_writeTimeout)) {
        setLastError("Write data timeout");
        return -1;
    }

    logDebug("Serial port sent data: " << data.toHex(' ') << " bytes: " << bytesWritten);

    return bytesWritten;
}

QByteArray SerialDevice::readAll()
{
    if (!isOpen()) {
        setLastError("Serial port not open");
        return QByteArray();
    }

    QByteArray data = m_serialPort->readAll();
    if (!data.isEmpty()) {
        logDebug("Serial port received data: " << data.toHex(' ') << " bytes: " << data.size() << "string: " << data);
    }

    return data;
}

QString SerialDevice::portName() const
{
    return m_serialPort ? m_serialPort->portName() : QString();
}

bool SerialDevice::setPortSettings(const QString& portName,
                                  QSerialPort::BaudRate baudRate,
                                  QSerialPort::DataBits dataBits,
                                  QSerialPort::Parity parity,
                                  QSerialPort::StopBits stopBits,
                                  QSerialPort::FlowControl flowControl)
{
    if (isOpen()) {
        setLastError("Cannot modify settings while serial port is open");
        return false;
    }

    m_serialPort->setPortName(portName);
    m_serialPort->setBaudRate(baudRate);
    m_serialPort->setDataBits(dataBits);
    m_serialPort->setParity(parity);
    m_serialPort->setStopBits(stopBits);
    m_serialPort->setFlowControl(flowControl);

    logInfo("Serial port parameters set: " << portName
            << " Baud rate: " << baudRate
            << " Data bits: " << dataBits
            << " Parity: " << parity
            << " Stop bits: " << stopBits
            << " Flow control: " << flowControl);

    return true;
}

void SerialDevice::setTimeout(int readTimeout, int writeTimeout)
{
    m_readTimeout = readTimeout;
    m_writeTimeout = writeTimeout;

    logInfo("Serial port timeout settings - Read: " << m_readTimeout << "ms, Write: " << m_writeTimeout << "ms");
}

QString SerialDevice::lastError() const
{
    return m_lastError;
}

void SerialDevice::clear()
{
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->clear();
        logDebug("Serial port buffer cleared");
    }
}

QStringList SerialDevice::availablePorts()
{
    QStringList portNames;
    const auto serialPortInfos = QSerialPortInfo::availablePorts();

    for (const QSerialPortInfo &portInfo : serialPortInfos) {
        portNames << portInfo.portName();
        logDebug("Available serial port: " << portInfo.portName()
                 << " Description: " << portInfo.description()
                 << " Manufacturer: " << portInfo.manufacturer()
                 << " System location: " << portInfo.systemLocation()
                 << " VID: " << QString::number(portInfo.vendorIdentifier(), 16)
                 << " PID: " << QString::number(portInfo.productIdentifier(), 16));
    }

    return portNames;
}

void SerialDevice::onDataReceived()
{
    QByteArray data = readAll();
    if (!data.isEmpty()) {
        emit dataReceived(data);
    }
}

void SerialDevice::onSerialError(QSerialPort::SerialPortError error)
{
    if (error == QSerialPort::NoError) {
        return;
    }

    QString errorString;
    switch (error) {
    case QSerialPort::DeviceNotFoundError:
        errorString = "Device not found";
        break;
    case QSerialPort::PermissionError:
        errorString = "Permission error";
        break;
    case QSerialPort::OpenError:
        errorString = "Open error";
        break;
    case QSerialPort::ParityError:
        errorString = "Parity error";
        break;
    case QSerialPort::FramingError:
        errorString = "Framing error";
        break;
    case QSerialPort::BreakConditionError:
        errorString = "Break condition error";
        break;
    case QSerialPort::WriteError:
        errorString = "Write error";
        break;
    case QSerialPort::ReadError:
        errorString = "Read error";
        break;
    case QSerialPort::ResourceError:
        errorString = "Resource error";
        break;
    case QSerialPort::UnsupportedOperationError:
        errorString = "Unsupported operation";
        break;
    case QSerialPort::TimeoutError:
        errorString = "Timeout error";
        break;
    case QSerialPort::NotOpenError:
        errorString = "Serial port not open";
        break;
    default:
        errorString = "Unknown error";
        break;
    }

    QString fullError = QString("Serial port error (%1): %2 - %3")
                       .arg(static_cast<int>(error))
                       .arg(errorString, m_serialPort->errorString());

    setLastError(fullError);

    emit errorOccurred(fullError);

    // 严重错误时自动关闭串口
    if (error == QSerialPort::ResourceError ||
        error == QSerialPort::DeviceNotFoundError ||
        error == QSerialPort::PermissionError) {
        close();
    }
}

void SerialDevice::onWriteTimeout()
{
    setLastError("Write data timeout");
    logWarnning("Serial port write timeout");
    emit errorOccurred(m_lastError);
}

void SerialDevice::setLastError(const QString& error)
{
    m_lastError = error;
    logError(error);  // 添加日志输出
}

